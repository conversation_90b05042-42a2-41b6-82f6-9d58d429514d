
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@font-face {
  font-family: 'SecularOne';
  src: url('/fonts/SecularOne-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Input Placeholder Alignment */
[dir="rtl"] input::placeholder {
  text-align: right;
}

@layer base {
  :root {
    --background: 0 0% 8%; /* Dark charcoal */
    --foreground: 0 0% 95%; /* Light grey/white */

    --card: 0 0% 12%; /* Slightly lighter charcoal */
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 95%;

    --primary: 0 0% 85%; /* Light grey */
    --primary-foreground: 0 0% 15%; /* Dark charcoal */

    --secondary: 0 0% 18%; /* Medium charcoal */
    --secondary-foreground: 0 0% 95%;

    --muted: 0 0% 22%; /* Lighter charcoal */
    --muted-foreground: 0 0% 70%; /* Medium grey */

    --accent: 0 0% 85%; /* Light grey accent */
    --accent-foreground: 0 0% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 22%; /* Medium charcoal border */
    --input: 0 0% 22%;
    --ring: 0 0% 85%; /* Light grey ring */

    --radius: 0.5rem;
  }

  .light {
    --background: 0 0% 98%; /* Very light grey */
    --foreground: 0 0% 20%; /* Dark grey */

    --card: 0 0% 96%; /* Light grey card */
    --card-foreground: 0 0% 20%;

    --popover: 0 0% 96%;
    --popover-foreground: 0 0% 20%;

    --primary: 0 0% 25%; /* Dark grey primary */
    --primary-foreground: 0 0% 98%; /* Very light grey */

    --secondary: 0 0% 92%; /* Light grey secondary */
    --secondary-foreground: 0 0% 25%;

    --muted: 0 0% 88%; /* Medium light grey */
    --muted-foreground: 0 0% 45%; /* Medium grey */

    --accent: 0 0% 25%; /* Dark grey accent */
    --accent-foreground: 0 0% 98%;

    --border: 0 0% 85%; /* Light grey border */
    --input: 0 0% 90%;
    --ring: 0 0% 25%; /* Dark grey ring */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background font-secular text-foreground antialiased;
    letter-spacing: -0.025em;
  }
  
  ::selection {
    @apply bg-primary text-primary-foreground;
  }

  .cosmic-gradient {
    background: linear-gradient(135deg, rgba(64, 64, 64, 0.2) 0%, rgba(32, 32, 32, 0.4) 100%);
  }

  .light .cosmic-gradient {
    background: linear-gradient(135deg, rgba(230, 230, 230, 0.8) 0%, rgba(200, 200, 200, 0.6) 100%);
  }

  .cosmic-glow {
    position: relative;
  }

  .cosmic-glow::before {
    content: "";
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    z-index: -1;
    border-radius: inherit;
  }

  .light .cosmic-glow::before {
    background: radial-gradient(circle at center, rgba(64, 64, 64, 0.08) 0%, transparent 70%);
  }

  .cosmic-grid {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 30px 30px;
  }

  .light .cosmic-grid {
    background-image: 
      linear-gradient(rgba(64, 64, 64, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(64, 64, 64, 0.08) 1px, transparent 1px);
  }

  .text-balance {
    text-wrap: balance;
  }
  
  .cosmic-glass {
    @apply backdrop-blur-sm bg-card border border-white/10 shadow-[0_0_15px_rgba(255,255,255,0.1)];
  }

  .light .cosmic-glass {
    @apply backdrop-blur-sm bg-card border border-black/10 shadow-[0_0_15px_rgba(0,0,0,0.05)];
  }
  
  .cosmic-card {
    @apply bg-card border border-border backdrop-blur-sm;
  }
  
  .nav-pill {
    @apply backdrop-blur-md bg-card border border-border rounded-full;
  }
  
  .nav-pill-item {
    @apply px-4 py-2 text-sm rounded-full transition-colors;
  }
  
  .nav-pill-item:hover {
    @apply bg-accent text-accent-foreground;
  }
  
  .nav-pill-item.active {
    @apply bg-accent text-accent-foreground;
  }
  
  .icon-glow {
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
    transition: filter 0.3s ease;
  }

  .light .icon-glow {
    filter: drop-shadow(0 0 6px rgba(64, 64, 64, 0.2));
  }
  
  .collapsible-trigger:hover .icon-glow {
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
  }

  .light .collapsible-trigger:hover .icon-glow {
    filter: drop-shadow(0 0 10px rgba(64, 64, 64, 0.3));
  }

  .task-card {
    @apply transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-lg hover:border-primary/30;
  }
  
  .task-card.dragging {
    @apply scale-105 shadow-lg border-primary opacity-70 rotate-1 z-10;
  }
  
  .task-card.dragged-over {
    @apply border-primary border-dashed;
  }
  
  .task-card-enter {
    @apply opacity-0 -translate-y-4;
  }
  
  .task-card-enter-active {
    @apply opacity-100 translate-y-0 transition-all duration-300 ease-out;
  }
  
  .task-card-exit {
    @apply opacity-100;
  }
  
  .task-card-exit-active {
    @apply opacity-0 translate-y-4 transition-all duration-300 ease-in;
  }
  
  .column-highlight {
    @apply bg-accent/20 transition-all duration-300;
  }

  /* Feature animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slideDown {
    animation: slideDown 0.4s ease-out;
  }

  .animate-slideUp {
    animation: slideUp 0.6s ease-out;
  }
}
