import React, { forwardRef } from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { cn } from '@/lib/utils';

export interface PhoneInputProps {
  value?: string;
  onChange?: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

const PhoneInputComponent = forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ className, error, ...props }, ref) => {
    return (
      <div className="relative">
        <PhoneInput
          {...props}
          defaultCountry="IL"
          international
          countryCallingCodeEditable={false}
          className={cn(
            'phone-input-container',
            error && 'phone-input-error',
            className
          )}
          inputComponent={React.forwardRef<HTMLInputElement, any>((inputProps, inputRef) => (
            <input
              {...inputProps}
              ref={inputRef}
              className={cn(
                'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
                error && 'border-destructive'
              )}
            />
          ))}
        />

      </div>
    );
  }
);

PhoneInputComponent.displayName = 'PhoneInput';

export { PhoneInputComponent as PhoneInput };
