import React from 'react';
import { Button } from '@/components/ui/button';

export interface PricingPlan {
  name: string;
  price: string;
  period?: string;
  description: string;
  features: string[];
  buttonText: string;
  buttonVariant: "default" | "outline";
  popular: boolean;
}

interface PricingCardProps {
  plan: PricingPlan;
  onSelect?: () => void;
  isSelected?: boolean;
  showButton?: boolean;
}

const PricingCard: React.FC<PricingCardProps> = ({ 
  plan, 
  onSelect, 
  isSelected = false, 
  showButton = true 
}) => {
  return (
    <div
      className={`p-6 rounded-xl border flex flex-col h-full transition-all duration-300 relative cursor-pointer ${
        plan.popular
          ? "border-primary/50 bg-card pricing-glow"
          : "border-border bg-card hover:shadow-md"
      } ${
        isSelected
          ? 'ring-2 ring-primary border-primary'
          : ''
      }`}
      onClick={onSelect}
    >
      {plan.popular && (
        <div className="absolute -top-4 left-1/2 -translate-x-1/2 px-4 py-1 bg-primary text-primary-foreground text-sm rounded-full font-medium">
          הכי פופולרי
        </div>
      )}
      
      <div className="mb-auto">
        <h3 className="text-2xl font-medium tracking-tighter mb-1 text-foreground">{plan.name}</h3>
        
        <div className="mb-4">
          <div className="text-3xl font-bold tracking-tighter text-foreground">{plan.price}</div>
          {plan.period && <div className="text-sm text-muted-foreground">{plan.period}</div>}
        </div>
        
        <p className="text-muted-foreground mb-6">{plan.description}</p>
        
        <div className="space-y-3 mb-8">
          {plan.features.map((feature, i) => (
            <div key={i} className="flex items-center gap-3">
              <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12L10 17L19 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <span className="text-sm text-foreground">{feature}</span>
            </div>
          ))}
        </div>
      </div>
      
      {showButton && (
        <div className="mt-6">
          <Button 
            className={
              plan.buttonVariant === "default" 
                ? "w-full bg-primary text-primary-foreground hover:bg-primary/90" 
                : "w-full border-border text-foreground hover:bg-muted"
            }
            variant={plan.buttonVariant}
          >
            {plan.buttonText}
          </Button>
        </div>
      )}
    </div>
  );
};

export default PricingCard;
