import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, AlertCircle, Building2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import Logo from '@/components/Logo';

const organizationSchema = z.object({
  name: z.string().min(2, 'שם הארגון חייב להכיל לפחות 2 תווים'),
  slug: z.string()
    .min(3, 'מזהה הארגון חייב להכיל לפחות 3 תווים')
    .max(50, 'מזהה הארגון לא יכול להכיל יותר מ-50 תווים')
    .regex(/^[a-z0-9-]+$/, 'מזהה הארגון יכול להכיל רק אותיות באנגלית, מספרים ומקפים'),
  domain: z.string().optional(),
  plan: z.enum(['free', 'advanced', 'enterprise']),
});

type OrganizationFormData = z.infer<typeof organizationSchema>;

const OrganizationSetup: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { createOrganization } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      plan: 'free',
    },
  });

  const organizationName = watch('name');

  // Auto-generate slug from organization name
  React.useEffect(() => {
    if (organizationName) {
      const slug = organizationName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setValue('slug', slug);
    }
  }, [organizationName, setValue]);

  const onSubmit = async (data: OrganizationFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: organization, error } = await createOrganization(data.name, data.slug);

      if (error) {
        if (error.code === '23505') {
          setError('מזהה הארגון כבר קיים. אנא בחר מזהה אחר.');
        } else {
          setError('אירעה שגיאה ביצירת הארגון. אנא נסה שוב.');
        }
        return;
      }

      toast({
        title: 'ארגון נוצר בהצלחה!',
        description: `ברוך הבא ל${data.name}`,
      });

      navigate('/dashboard');
    } catch (err) {
      setError('אירעה שגיאה בלתי צפויה. אנא נסה שוב.');
    } finally {
      setIsLoading(false);
    }
  };

  const plans = [
    {
      id: 'free',
      name: 'חינם',
      description: 'עד 50 חשבוניות, 5 משתמשים',
      price: '₪0',
    },
    {
      id: 'advanced',
      name: 'מתקדם',
      description: 'תכונות AI, חשבוניות ללא הגבלה',
      price: '₪99/חודש',
    },
    {
      id: 'enterprise',
      name: 'ארגוני',
      description: 'פתרון מותאם אישית',
      price: 'צור קשר',
    },
  ];

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <div className="w-full max-w-2xl space-y-6">
        {/* Logo */}
        <div className="text-center">
          <Logo />
          <h1 className="mt-4 text-2xl font-bold">הגדרת ארגון</h1>
          <p className="text-muted-foreground">
            צור את הארגון שלך כדי להתחיל להשתמש במערכת
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center flex items-center justify-center gap-2">
              <Building2 className="h-5 w-5" />
              פרטי הארגון
            </CardTitle>
            <CardDescription className="text-center">
              מלא את הפרטים הבסיסיים של הארגון שלך
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">שם הארגון</Label>
                <Input
                  id="name"
                  placeholder="שם החברה או הארגון שלך"
                  {...register('name')}
                  className={errors.name ? 'border-destructive' : ''}
                  disabled={isLoading}
                />
                {errors.name && (
                  <p className="text-sm text-destructive">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">מזהה ארגון (URL)</Label>
                <div className="flex">
                  <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-input bg-muted text-muted-foreground text-sm">
                    yourapp.com/
                  </span>
                  <Input
                    id="slug"
                    placeholder="organization-name"
                    {...register('slug')}
                    className={`rounded-l-none ${errors.slug ? 'border-destructive' : ''}`}
                    disabled={isLoading}
                  />
                </div>
                {errors.slug && (
                  <p className="text-sm text-destructive">{errors.slug.message}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  זה יהיה הכתובת הייחודית של הארגון שלך במערכת
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="domain">דומיין (אופציונלי)</Label>
                <Input
                  id="domain"
                  placeholder="example.com"
                  {...register('domain')}
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground">
                  דומיין מותאם אישית לארגון שלך
                </p>
              </div>

              <div className="space-y-4">
                <Label>בחר תוכנית</Label>
                <div className="grid gap-4 md:grid-cols-3">
                  {plans.map((plan) => (
                    <Card 
                      key={plan.id}
                      className={`cursor-pointer transition-all ${
                        watch('plan') === plan.id 
                          ? 'ring-2 ring-primary border-primary' 
                          : 'hover:shadow-md'
                      }`}
                      onClick={() => setValue('plan', plan.id as any)}
                    >
                      <CardHeader className="text-center pb-2">
                        <CardTitle className="text-lg">{plan.name}</CardTitle>
                        <div className="text-2xl font-bold text-primary">{plan.price}</div>
                      </CardHeader>
                      <CardContent className="text-center">
                        <p className="text-sm text-muted-foreground">{plan.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    יוצר ארגון...
                  </>
                ) : (
                  'צור ארגון והתחל'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            תוכל לשנות את ההגדרות האלה בכל עת מהגדרות הארגון
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrganizationSetup;
