import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, AlertCircle, Building2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import Logo from '@/components/Logo';

const organizationSchema = z.object({
  plan: z.enum(['free', 'advanced', 'enterprise']),
});

type OrganizationFormData = z.infer<typeof organizationSchema>;

const OrganizationSetup: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { createOrganization } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      plan: 'free',
    },
  });

  const onSubmit = async (data: OrganizationFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // Get organization name from survey data
      const surveyData = JSON.parse(localStorage.getItem('surveyData') || '{}');
      const companyName = surveyData.companyName || 'החברה שלי';
      const slug = companyName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim() || 'my-company';

      const { data: organization, error } = await createOrganization(companyName, slug);

      if (error) {
        if (error.code === '23505') {
          setError('מזהה הארגון כבר קיים. אנא נסה שוב.');
        } else {
          setError('אירעה שגיאה ביצירת הארגון. אנא נסה שוב.');
        }
        return;
      }

      toast({
        title: 'התוכנית נבחרה בהצלחה!',
        description: `ברוך הבא ל${companyName}`,
      });

      navigate('/app');
    } catch (err) {
      setError('אירעה שגיאה בלתי צפויה. אנא נסה שוב.');
    } finally {
      setIsLoading(false);
    }
  };

  const plans = [
    {
      id: 'free',
      name: 'חינם',
      description: 'חשבוניות ללא הגבלה, עד 5 משתמשים',
      price: '₪0',
    },
    {
      id: 'advanced',
      name: 'מתקדם',
      description: 'כל התכונות החינמיות + תכונות AI',
      price: '₪99/חודש',
    },
    {
      id: 'enterprise',
      name: 'ארגוני',
      description: 'פתרון מותאם אישית',
      price: 'צור קשר',
    },
  ];

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background" dir="rtl">
      <div className="w-full max-w-2xl space-y-6">
        {/* Logo */}
        <div className="text-center">
          <div className="flex justify-center">
            <Logo />
          </div>
          <h1 className="mt-4 text-2xl font-bold">בחירת תוכנית</h1>
          <p className="text-muted-foreground">
            בחר את התוכנית המתאימה לעסק שלך
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center flex items-center justify-center gap-2">
              <Building2 className="h-5 w-5" />
              בחירת תוכנית
            </CardTitle>
            <CardDescription className="text-center">
              בחר את התוכנית המתאימה לעסק שלך
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  {plans.map((plan) => (
                    <Card
                      key={plan.id}
                      className={`cursor-pointer transition-all ${
                        watch('plan') === plan.id
                          ? 'ring-2 ring-primary border-primary'
                          : 'hover:shadow-md'
                      }`}
                      onClick={() => setValue('plan', plan.id as any)}
                    >
                      <CardHeader className="text-center pb-2">
                        <CardTitle className="text-lg">{plan.name}</CardTitle>
                        <div className="text-2xl font-bold text-primary">{plan.price}</div>
                      </CardHeader>
                      <CardContent className="text-center">
                        <p className="text-sm text-muted-foreground">{plan.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    מגדיר חשבון...
                  </>
                ) : (
                  'המשך עם התוכנית שנבחרה'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            תוכל לשנות את התוכנית בכל עת מהגדרות החשבון
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrganizationSetup;
