import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PasswordInput } from '@/components/ui/password-input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import Logo from '@/components/Logo';
import { SignUpSurvey } from '@/components/auth/SignUpSurvey';
import { PhoneInput } from '@/components/ui/phone-input';

const signUpSchema = z.object({
  firstName: z.string().min(2, 'שם פרטי חייב להכיל לפחות 2 תווים'),
  lastName: z.string().min(2, 'שם משפחה חייב להכיל לפחות 2 תווים'),
  email: z.string().email('כתובת אימייל לא תקינה'),
  phone: z.string().min(10, 'מספר טלפון חייב להכיל לפחות 10 ספרות'),
  password: z.string()
    .min(8, 'סיסמה חייבת להכיל לפחות 8 תווים')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'סיסמה חייבת להכיל אות גדולה, אות קטנה ומספר'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, 'חובה לאשר את התנאים'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'הסיסמאות אינן תואמות',
  path: ['confirmPassword'],
});

type SignUpFormData = z.infer<typeof signUpSchema>;

const SignUp: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showSurvey, setShowSurvey] = useState(false);
  const [signUpData, setSignUpData] = useState<SignUpFormData | null>(null);

  const { signUp } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
  });

  const acceptTerms = watch('acceptTerms');

  const onSubmit = async (data: SignUpFormData) => {
    setSignUpData(data);
    setShowSurvey(true);
  };

  const handleSurveyComplete = async (surveyData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!signUpData) return;

      const { error } = await signUp(signUpData.email, signUpData.password, {
        first_name: signUpData.firstName,
        last_name: signUpData.lastName,
        email: signUpData.email,
        phone: signUpData.phone,
        // Add survey data to user metadata
        company_name: surveyData.companyName,
        annual_revenue: surveyData.annualRevenue,
        accounting_services: surveyData.accountingServices,
        business_loan: surveyData.businessLoan,
      });

      if (error) {
        setError(getErrorMessage(error.message));
        setShowSurvey(false);
        return;
      }

      setSuccess(true);
      toast({
        title: 'הרשמה בוצעה בהצלחה!',
        description: 'אנא בדוק את האימייל שלך לאימות החשבון.',
      });

      // Redirect to sign in after a delay
      setTimeout(() => {
        navigate('/signin');
      }, 3000);
    } catch (err) {
      setError('אירעה שגיאה בלתי צפויה. אנא נסה שוב.');
      setShowSurvey(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSurveyBack = () => {
    setShowSurvey(false);
    setSignUpData(null);
  };

  const getErrorMessage = (errorMessage: string): string => {
    if (errorMessage.includes('User already registered')) {
      return 'כתובת האימייל כבר רשומה במערכת';
    }
    if (errorMessage.includes('Password should be at least')) {
      return 'סיסמה חייבת להכיל לפחות 8 תווים';
    }
    if (errorMessage.includes('Invalid email')) {
      return 'כתובת אימייל לא תקינה';
    }
    return 'אירעה שגיאה בהרשמה. אנא נסה שוב';
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-background" dir="rtl">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl">הרשמה בוצעה בהצלחה!</CardTitle>
            <CardDescription>
              נשלח אימייל אימות לכתובת שהזנת. אנא לחץ על הקישור באימייל כדי לאמת את החשבון שלך.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                לא קיבלת אימייל? בדוק בתיקיית הספאם או נסה שוב.
              </p>
              <Button asChild className="w-full">
                <Link to="/signin">המשך להתחברות</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (showSurvey) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-background" dir="rtl">
        <div className="w-full max-w-md space-y-6">
          {/* Logo */}
          <div className="text-center flex flex-col items-center">
            <div className="flex justify-center mb-4">
              <Logo />
            </div>
            <h1 className="text-2xl font-bold">כמה שאלות נוספות</h1>
            <p className="text-muted-foreground mt-2">
              עוד רק כמה פרטים כדי להתאים את המערכת בדיוק בשבילך
            </p>
          </div>

          <SignUpSurvey
            onComplete={handleSurveyComplete}
            onBack={handleSurveyBack}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background" dir="rtl">
      <div className="w-full max-w-md space-y-6">
        {/* Logo */}
        <div className="text-center flex flex-col items-center">
          <div className="flex justify-center mb-4">
            <Logo />
          </div>
          <h1 className="text-2xl font-bold">הרשמה למערכת</h1>
          <p className="text-muted-foreground mt-2">
            צור חשבון חדש כדי להתחיל להשתמש במערכת
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">הרשמה</CardTitle>
            <CardDescription className="text-center">
              מלא את הפרטים שלך כדי ליצור חשבון חדש
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">שם פרטי *</Label>
                  <Input
                    id="firstName"
                    placeholder="שם פרטי"
                    {...register('firstName')}
                    className={errors.firstName ? 'border-destructive' : ''}
                    disabled={isLoading}
                    required
                  />
                  {errors.firstName && (
                    <p className="text-sm text-destructive">{errors.firstName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">שם משפחה *</Label>
                  <Input
                    id="lastName"
                    placeholder="שם משפחה"
                    {...register('lastName')}
                    className={errors.lastName ? 'border-destructive' : ''}
                    disabled={isLoading}
                    required
                  />
                  {errors.lastName && (
                    <p className="text-sm text-destructive">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">כתובת אימייל *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={errors.email ? 'border-destructive' : ''}
                  disabled={isLoading}
                  required
                />
                {errors.email && (
                  <p className="text-sm text-destructive">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">מספר טלפון *</Label>
                <PhoneInput
                  value={watch('phone')}
                  onChange={(value) => setValue('phone', value || '')}
                  placeholder="הזן מספר טלפון"
                  error={!!errors.phone}
                  disabled={isLoading}
                />
                {errors.phone && (
                  <p className="text-sm text-destructive">{errors.phone.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">סיסמה *</Label>
                <PasswordInput
                  id="password"
                  placeholder="הזן סיסמה"
                  {...register('password')}
                  error={!!errors.password}
                  disabled={isLoading}
                />
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">אימות סיסמה *</Label>
                <PasswordInput
                  id="confirmPassword"
                  placeholder="הזן סיסמה שוב"
                  {...register('confirmPassword')}
                  error={!!errors.confirmPassword}
                  disabled={isLoading}
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-destructive">{errors.confirmPassword.message}</p>
                )}
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="acceptTerms"
                  checked={acceptTerms}
                  onCheckedChange={(checked) => setValue('acceptTerms', checked as boolean)}
                  disabled={isLoading}
                />
                <Label htmlFor="acceptTerms" className="text-sm">
                  אני מסכים ל
                  <Link to="/terms" className="text-primary hover:underline mx-1">
                    תנאי השימוש
                  </Link>
                  ול
                  <Link to="/privacy" className="text-primary hover:underline mx-1">
                    מדיניות הפרטיות
                  </Link>
                </Label>
              </div>
              {errors.acceptTerms && (
                <p className="text-sm text-destructive">{errors.acceptTerms.message}</p>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    נרשם...
                  </>
                ) : (
                  'הירשם'
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                כבר יש לך חשבון?{' '}
                <Link
                  to="/signin"
                  className="text-primary hover:underline font-medium"
                >
                  התחבר כאן
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <Link
            to="/"
            className="text-sm text-muted-foreground hover:text-foreground"
          >
            ← חזור לעמוד הבית
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
