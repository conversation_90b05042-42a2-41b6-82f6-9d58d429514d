
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, <PERSON><PERSON>ontent, Card<PERSON>ooter, CardHeader } from '@/components/ui/card';

const Pricing = () => {
  const plans = [
    {
      name: "עסק חדש",
      price: "חינם",
      description: "מושלם לעסקים קטנים שמתחילים את המסע הפיננסי שלהם",
      features: [
        "חשבוניות ללא הגבלה",
        "עד 5 משתמשים בחברה",
        "מעקב הוצאות ללא הגבלה",
        "לוחות דוחות",
        "תמיכה במייל"
      ],
      buttonText: "התחל עכשיו",
      buttonVariant: "outline",
      popular: false
    },
    {
      name: "מתקדם",
      price: "₪99",
      period: "לחודש",
      description: "כל התכונות החינמיות + תכונות AI מתקדמות",
      features: [
        "כל התכונות החינמיות",
        "עוזר AI בוואטסאפ (עד 5 משתמשים)",
        "בודק מיילים AI (עד 100 מסמכים)",
        "אינטגרציה עם משרדי רואי חשבון",
        "תמיכה בוואטסאפ"
      ],
      buttonText: "התחל ניסיון 14 יום",
      buttonVariant: "default",
      popular: true
    },
    {
      name: "ארגוני",
      price: "צור קשר",
      description: "לארגונים גדולים עם פעילות פיננסית מורכבת",
      features: [
        "יכולת התחברות עם API",
        "נציג תמיכה ייעודי",
        "מספרי עוזר AI ללא הגבלה",
        "בודק מיילים ללא הגבלה"
      ],
      buttonText: "צור קשר",
      buttonVariant: "outline",
      popular: false
    }
  ];
  
  return (
    <section id="pricing" className="w-full py-20 px-6 md:px-12 bg-background">
      <div className="max-w-7xl mx-auto space-y-16">
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-medium tracking-tighter text-foreground">
            תמחור שקוף לכל שלב
          </h2>
          <p className="text-muted-foreground text-lg">
            הגדל את הפעילות הפיננסית שלך עם תוכניות שגדלות עם העסק שלך
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div 
              key={index}
              className={`p-6 rounded-xl border flex flex-col h-full ${
                plan.popular 
                  ? "border-primary/50 cosmic-glow bg-card" 
                  : "border-border cosmic-gradient bg-card"
              } transition-all duration-300 relative`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2 px-4 py-1 bg-primary text-primary-foreground text-sm rounded-full font-medium">
                  הכי פופולרי
                </div>
              )}
              
              <div className="mb-auto">
                <h3 className="text-2xl font-medium tracking-tighter mb-1 text-foreground">{plan.name}</h3>
                
                <div className="mb-4">
                  <div className="text-3xl font-bold tracking-tighter text-foreground">{plan.price}</div>
                  {plan.period && <div className="text-sm text-muted-foreground">{plan.period}</div>}
                </div>
                
                <p className="text-muted-foreground mb-6">{plan.description}</p>
                
                <div className="space-y-3 mb-8">
                  {plan.features.map((feature, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5 12L10 17L19 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <span className="text-sm text-foreground">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mt-6">
                <Button 
                  className={
                    plan.buttonVariant === "default" 
                      ? "w-full bg-primary text-primary-foreground hover:bg-primary/90" 
                      : "w-full border-border text-foreground hover:bg-muted"
                  }
                  variant={plan.buttonVariant as "default" | "outline"}
                >
                  {plan.buttonText}
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center text-muted-foreground">
          יש שאלות? <a href="#" className="text-primary hover:underline">צור קשר עם צוות המכירות שלנו</a>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
